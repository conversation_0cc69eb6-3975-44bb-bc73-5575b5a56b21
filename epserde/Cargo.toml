[package]
name = "epserde"
authors = [
	"<PERSON><PERSON><PERSON> <<EMAIL>>",
	"<PERSON><PERSON> <<EMAIL>>",
]
description = "ε-serde is an ε-copy (i.e., almost zero-copy) serialization/deserialization framework"
version = "0.8.0"
edition = "2021"
repository = "https://github.com/vigna/epserde-rs/"
license = "Apache-2.0 OR LGPL-2.1-or-later"
readme = "README.md"
keywords = ["serialization", "zero-copy", "mmap"]

[dependencies]
mmap-rs = { version = "0.6.0", optional = true }
bitflags = { version = "2.4.2", default-features = false }
xxhash-rust = { version = "0.8.8", default-features = false, features = [
	"xxh3",
] }
epserde-derive = { workspace = true, optional = true }
anyhow = "1.0.79"
thiserror = "2.0.11"
sealed = "0.6.0"
maligned = "0.2.1"
common_traits = "0.11.2"
yoke = { version = "0.8.0", features = ["derive"] }
stable_deref_trait = "1.2.0"
mem_dbg = { version = "0.3.0", features = [
	"maligned",
	"derive",
], default-features = false }

[features]
default = ["std", "mmap", "derive"]
derive = ["epserde-derive"]
std = ["alloc", "mem_dbg/std"]
alloc = []
mmap = ["mmap-rs", "mem_dbg/mmap-rs"]
